import sys
import os
from PyQt6.QtWidgets import QA<PERSON><PERSON>, QSplashScreen, QLabel, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QBrush, QPen
from resource_path import resource_path

class SplashScreen(QSplashScreen):
    def __init__(self):
        print("Iniciando criação da splash screen...")

        # Criar um pixmap personalizado com fundo branco e bordas arredondadas
        base_pixmap = QPixmap(500, 400)
        base_pixmap.fill(Qt.GlobalColor.transparent)
        print(f"Pixmap criado: {base_pixmap.width()}x{base_pixmap.height()}")

        # Carregar a logo
        logo_path = resource_path('logo.png')
        print(f"Tentando carregar logo de: {logo_path}")
        logo_pixmap = QPixmap(logo_path)
        if logo_pixmap.isNull():
            print("ERRO: Não foi possível carregar a logo!")
            # Criar um pixmap de fallback
            logo_pixmap = QPixmap(150, 150)
            logo_pixmap.fill(Qt.GlobalColor.blue)
        else:
            print("Logo carregada com sucesso!")

        scaled_logo = logo_pixmap.scaled(150, 150, Qt.AspectRatioMode.KeepAspectRatio,
                                        Qt.TransformationMode.SmoothTransformation)

        # Desenhar o fundo e a logo
        painter = QPainter(base_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Desenhar fundo branco com bordas arredondadas e sombra
        painter.setBrush(QBrush(QColor("#ffffff")))
        painter.setPen(QPen(QColor("#cccccc"), 3))
        painter.drawRoundedRect(10, 10, 480, 380, 15, 15)

        # Desenhar a logo centralizada
        logo_x = (base_pixmap.width() - scaled_logo.width()) // 2
        logo_y = 60
        painter.drawPixmap(logo_x, logo_y, scaled_logo)

        # Desenhar o título principal
        painter.setFont(QFont('Segoe UI', 20, QFont.Weight.Bold))
        painter.setPen(QPen(QColor("#2c3e50")))
        painter.drawText(20, logo_y + scaled_logo.height() + 30,
                        base_pixmap.width() - 40, 40,
                        Qt.AlignmentFlag.AlignCenter, "Prospector")

        # Desenhar o subtítulo
        painter.setFont(QFont('Segoe UI', 12))
        painter.setPen(QPen(QColor("#7f8c8d")))
        painter.drawText(20, logo_y + scaled_logo.height() + 70,
                        base_pixmap.width() - 40, 30,
                        Qt.AlignmentFlag.AlignCenter, "Captura de Leads do Google Maps")

        # Desenhar a versão
        painter.setFont(QFont('Segoe UI', 10))
        painter.setPen(QPen(QColor("#95a5a6")))
        painter.drawText(20, logo_y + scaled_logo.height() + 110,
                        base_pixmap.width() - 40, 20,
                        Qt.AlignmentFlag.AlignCenter, "v1.0.0")

        # Desenhar mensagem de carregamento
        painter.setFont(QFont('Segoe UI', 11, QFont.Weight.Bold))
        painter.setPen(QPen(QColor("#3498db")))
        painter.drawText(20, base_pixmap.height() - 50,
                        base_pixmap.width() - 40, 20,
                        Qt.AlignmentFlag.AlignCenter, "Carregando aplicação...")

        painter.end()
        print("Desenho da splash screen concluído")

        super().__init__(base_pixmap)
        print("QSplashScreen inicializado")

        # Configurar janela
        self.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)
        self.setWindowFlag(Qt.WindowType.FramelessWindowHint)
        print("Flags da janela configuradas")

        # Centralizar na tela
        self.center_on_screen()
        print("Splash screen centralizada")

    def center_on_screen(self):
        """Centraliza a splash screen na tela"""
        from PyQt6.QtWidgets import QApplication
        screen = QApplication.primaryScreen()
        if screen:
            screen_geometry = screen.geometry()
            splash_geometry = self.geometry()
            x = (screen_geometry.width() - splash_geometry.width()) // 2
            y = (screen_geometry.height() - splash_geometry.height()) // 2
            self.move(x, y)

    def mousePressEvent(self, event):
        # Permitir fechar a tela de splash com um clique
        self.close()

def show_splash(app, duration=3000):
    """Show splash screen for the specified duration in milliseconds"""
    print(f"Criando splash screen com duração de {duration}ms...")
    splash = SplashScreen()

    print("Exibindo splash screen...")
    splash.show()
    splash.raise_()  # Trazer para frente
    splash.activateWindow()  # Ativar a janela

    print("Processando eventos...")
    # Processar eventos para garantir que a splash apareça
    app.processEvents()

    print(f"Agendando fechamento da splash em {duration}ms...")
    # Close the splash screen after duration
    QTimer.singleShot(duration, splash.close)

    print("Splash screen configurada e exibida!")
    return splash

if __name__ == "__main__":
    # Test the splash screen
    app = QApplication(sys.argv)
    splash = show_splash(app, 5000)  # 5 segundos para teste
    print("Splash screen criada e exibida")
    sys.exit(app.exec())
