#!/usr/bin/env python3
"""
Teste simples para verificar se a splash screen funciona
"""
import sys
import time
from PyQt6.QtWidgets import QApplication, QSplashScreen, QMainWindow, QLabel
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QBrush, QPen

def create_simple_splash():
    """Cria uma splash screen simples para teste"""
    # Criar um pixmap simples
    pixmap = QPixmap(400, 300)
    pixmap.fill(Qt.GlobalColor.white)
    
    # Desenhar texto simples
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # Fundo azul
    painter.setBrush(QBrush(QColor("#3498db")))
    painter.setPen(QPen(QColor("#2980b9"), 2))
    painter.drawRoundedRect(10, 10, 380, 280, 10, 10)
    
    # Texto
    painter.setFont(QFont('Arial', 24, QFont.Weight.Bold))
    painter.setPen(QPen(QColor("#ffffff")))
    painter.drawText(20, 50, 360, 50, Qt.AlignmentFlag.AlignCenter, "TESTE SPLASH")
    
    painter.setFont(QFont('Arial', 14))
    painter.drawText(20, 120, 360, 30, Qt.<PERSON>gnmentFlag.<PERSON>gnCenter, "<PERSON>egando aplica<PERSON>...")
    
    painter.setFont(QFont('<PERSON>l', 12))
    painter.drawText(20, 200, 360, 30, Qt.AlignmentFlag.AlignCenter, "Se você está vendo isso, a splash funciona!")
    
    painter.end()
    
    return pixmap

def main():
    print("Iniciando teste da splash screen...")
    
    app = QApplication(sys.argv)
    
    # Criar splash screen simples
    splash_pixmap = create_simple_splash()
    splash = QSplashScreen(splash_pixmap)
    
    # Configurar splash
    splash.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)
    splash.setWindowFlag(Qt.WindowType.FramelessWindowHint)
    
    print("Exibindo splash screen...")
    splash.show()
    splash.raise_()
    splash.activateWindow()
    
    # Processar eventos
    app.processEvents()
    
    # Criar janela principal simples
    main_window = QMainWindow()
    main_window.setWindowTitle("Janela Principal - Teste")
    main_window.resize(600, 400)
    
    label = QLabel("Aplicação carregada com sucesso!")
    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    label.setFont(QFont('Arial', 16))
    main_window.setCentralWidget(label)
    
    # Função para mostrar janela principal
    def show_main():
        print("Fechando splash e mostrando janela principal...")
        splash.close()
        main_window.show()
        main_window.raise_()
        main_window.activateWindow()
    
    # Mostrar janela principal após 3 segundos
    QTimer.singleShot(3000, show_main)
    
    print("Iniciando loop de eventos...")
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
