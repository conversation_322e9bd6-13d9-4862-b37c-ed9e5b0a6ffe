#!/usr/bin/env python3
"""
Teste simples para verificar se as modificações da interface estão funcionando
"""

import sys
import os

# Adicionar o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QComboBox, QLineEdit, QPushButton
    from PyQt6.QtCore import Qt

    print("✓ PyQt6 importado com sucesso")

    # Testar as constantes do ui.py
    try:
        from ui import ESTADOS_BRASILEIROS, BAIRROS_POPULARES, obter_bairros_estado
        print("✓ Constantes importadas com sucesso")
        print(f"✓ Total de estados: {len(ESTADOS_BRASILEIROS)}")
        print(f"✓ Estados com bairros definidos: {len(BAIRROS_POPULARES)}")

        # Testar a função obter_bairros_estado
        bairros_sp = obter_bairros_estado("São Paulo (SP)")
        print(f"✓ Bairros de SP: {len(bairros_sp)} encontrados")

        bairros_default = obter_bairros_estado("Estado Inexistente")
        print(f"✓ Bairros padrão: {len(bairros_default)} encontrados")

    except Exception as e:
        print(f"✗ Erro ao importar constantes: {e}")
        sys.exit(1)

    # Criar uma aplicação de teste
    app = QApplication(sys.argv)

    # Criar janela de teste
    window = QWidget()
    window.setWindowTitle("Teste - Interface Modificada")
    window.showMaximized()  # Abrir em tela cheia

    layout = QVBoxLayout()

    # Label de título
    title = QLabel("Teste da Interface Modificada")
    title.setAlignment(Qt.AlignmentFlag.AlignCenter)
    layout.addWidget(title)

    # Combo de estados
    label_estado = QLabel("Estado:")
    combo_estado = QComboBox()
    combo_estado.addItems(ESTADOS_BRASILEIROS)
    combo_estado.setCurrentText("São Paulo (SP)")
    layout.addWidget(label_estado)
    layout.addWidget(combo_estado)

    # Campo de bairro
    label_bairro = QLabel("Bairro:")
    entrada_bairro = QLineEdit()
    entrada_bairro.setPlaceholderText("Digite o bairro")
    layout.addWidget(label_bairro)
    layout.addWidget(entrada_bairro)

    # Função para atualizar bairros
    def atualizar_bairros():
        estado_selecionado = combo_estado.currentText()
        bairros = obter_bairros_estado(estado_selecionado)
        print(f"Estado selecionado: {estado_selecionado}")
        print(f"Bairros disponíveis: {len(bairros)}")

        # Configurar autocomplete
        from PyQt6.QtWidgets import QCompleter
        completer = QCompleter(bairros)
        completer.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        entrada_bairro.setCompleter(completer)

    # Conectar o sinal
    combo_estado.currentTextChanged.connect(atualizar_bairros)

    # Configurar autocomplete inicial
    atualizar_bairros()

    # Botão de teste
    botao_teste = QPushButton("Testar Seleção")

    def testar_selecao():
        estado = combo_estado.currentText()
        bairro = entrada_bairro.text()
        print(f"Estado: {estado}")
        print(f"Bairro: {bairro}")
        print(f"Localização: {bairro}, {estado}")

    botao_teste.clicked.connect(testar_selecao)
    layout.addWidget(botao_teste)

    # Aplicar estilos para melhor visualização
    window.setStyleSheet("""
        QWidget {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Arial, sans-serif;
        }
        QComboBox {
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background-color: white;
            color: #495057;
            margin-bottom: 5px;
            font-size: 13px;
            min-height: 20px;
        }
        QComboBox QAbstractItemView {
            background-color: white;
            color: #495057;
            border: 1px solid #ced4da;
            border-radius: 6px;
            selection-background-color: #e3f2fd;
            selection-color: #1976d2;
            padding: 5px;
        }
        QComboBox QAbstractItemView::item {
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        QComboBox QAbstractItemView::item:hover {
            background-color: #f5f5f5;
            color: #333;
        }
        QComboBox QAbstractItemView::item:selected {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        QLineEdit {
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            background-color: white;
            margin-bottom: 5px;
            font-size: 13px;
            min-height: 20px;
        }
        QLabel {
            color: #495057;
            font-weight: 500;
            margin-bottom: 5px;
            padding: 2px;
            font-size: 13px;
        }
        QPushButton {
            background-color: #4dabf7;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #339af0;
        }
    """)

    window.setLayout(layout)

    print("✓ Interface de teste criada com sucesso")
    print("✓ Todas as modificações estão funcionando!")
    print("\nPara testar:")
    print("1. Selecione um estado diferente")
    print("2. Digite um bairro (deve aparecer autocomplete)")
    print("3. Clique em 'Testar Seleção' para ver os valores")

    window.show()

    # Não executar o loop de eventos no teste automatizado
    # sys.exit(app.exec())

    print("✓ Teste concluído com sucesso!")

except ImportError as e:
    print(f"✗ Erro de importação: {e}")
    print("Verifique se o PyQt6 está instalado: pip install PyQt6")
    sys.exit(1)
except Exception as e:
    print(f"✗ Erro inesperado: {e}")
    sys.exit(1)
