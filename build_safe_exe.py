import os
import sys
import subprocess
import shutil
import time
from datetime import datetime

def print_header(text):
    """Imprime um cabeçalho formatado"""
    print("\n" + "=" * 70)
    print(f" {text} ".center(70, "="))
    print("=" * 70 + "\n")

def check_requirements():
    """Verifica se todas as dependências estão instaladas"""
    print_header("VERIFICANDO DEPENDÊNCIAS")

    required_packages = [
        "pyinstaller",
        "pywin32",
        "pillow"
    ]

    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} já está instalado")
        except ImportError:
            print(f"✗ {package} não encontrado. Instalando...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} instalado com sucesso")

    print("\nTodas as dependências estão instaladas!")

def clean_previous_builds():
    """Limpa builds anteriores"""
    print_header("LIMPANDO BUILDS ANTERIORES")

    # Diretórios para limpar
    dirs_to_clean = ['build', 'dist']

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"Removendo diretório {dir_name}...")
            try:
                shutil.rmtree(dir_name)
                print(f"✓ Diretório {dir_name} removido com sucesso")
            except Exception as e:
                print(f"✗ Erro ao remover {dir_name}: {str(e)}")
        else:
            print(f"✓ Diretório {dir_name} não existe, nada a limpar")

    # Remover arquivos .spec
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        try:
            os.remove(spec_file)
            print(f"✓ Arquivo {spec_file} removido com sucesso")
        except Exception as e:
            print(f"✗ Erro ao remover {spec_file}: {str(e)}")

    print("\nLimpeza concluída!")

def build_executable():
    """Compila o executável com configurações otimizadas"""
    print_header("COMPILANDO EXECUTÁVEL")

    # Nome do arquivo principal
    main_file = "ui.py"

    # Nome do executável de saída
    output_name = "LeadFinder"

    # Arquivos adicionais
    additional_files = [
        "logo.png",
        "file_version_info.txt"
    ]

    # Construir a lista de arquivos adicionais
    add_data_params = []
    for file in additional_files:
        if os.path.exists(file):
            # Usar formato correto para adicionar dados
            # No Windows: arquivo;destino
            add_data_params.append(f"--add-data={file};.")
            print(f"✓ Adicionando arquivo: {file}")

            # Adicionar também como recurso para garantir que seja incluído
            if file.endswith('.png'):
                add_data_params.append(f"--add-binary={file};.")
                print(f"✓ Adicionando também como recurso binário: {file}")
        else:
            print(f"✗ Arquivo não encontrado: {file}")
            return False

    # Verificar se o arquivo principal existe
    if not os.path.exists(main_file):
        print(f"✗ Arquivo principal não encontrado: {main_file}")
        return False

    # Construir o comando PyInstaller com opções otimizadas
    pyinstaller_cmd = [
        "pyinstaller",
        "--noconfirm",
        "--clean",
        "--onefile",
        "--windowed",
        f"--icon=logo.png",
        f"--name={output_name}",
        "--version-file=file_version_info.txt",
        "--uac-admin",
        # Nota: A opção --key foi removida no PyInstaller v6.0
        "--noupx",  # Evitar compressão UPX que pode acionar antivírus
        "--disable-windowed-traceback",  # Reduzir informações de depuração
        *add_data_params,
        main_file
    ]

    # Executar o comando
    print("\nIniciando compilação com PyInstaller...\n")
    print(f"Comando: {' '.join(pyinstaller_cmd)}\n")

    try:
        subprocess.check_call(pyinstaller_cmd)
        print("\n✓ Compilação concluída com sucesso!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Erro durante a compilação: {str(e)}")
        return False

def verify_build():
    """Verifica se o build foi criado corretamente"""
    print_header("VERIFICANDO BUILD")

    exe_path = os.path.join("dist", "LeadFinder.exe")

    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # Tamanho em MB
        print(f"✓ Executável criado: {exe_path}")
        print(f"✓ Tamanho do arquivo: {file_size:.2f} MB")

        # Verificar data de criação
        creation_time = os.path.getctime(exe_path)
        creation_date = datetime.fromtimestamp(creation_time).strftime('%Y-%m-%d %H:%M:%S')
        print(f"✓ Data de criação: {creation_date}")

        return True
    else:
        print(f"✗ Executável não encontrado: {exe_path}")
        return False

def main():
    """Função principal"""
    print_header("Prospector - COMPILAÇÃO SEGURA")
    print("Este script irá criar um executável otimizado para evitar detecções de antivírus.")
    print("Autor: Lead Capture Tools")
    print("Data: " + datetime.now().strftime("%Y-%m-%d"))

    # Verificar dependências
    check_requirements()

    # Limpar builds anteriores
    clean_previous_builds()

    # Compilar o executável
    if build_executable():
        # Verificar o build
        if verify_build():
            print_header("COMPILAÇÃO CONCLUÍDA COM SUCESSO")
            print("O executável foi criado com sucesso e está pronto para uso.")
            print("\nLocalização do executável: dist/LeadFinder.exe")
            print("\nRecomendações:")
            print("1. Teste o executável em um ambiente controlado")
            print("2. Caso ainda seja detectado como falso positivo, considere:")
            print("   - Adicionar exceções no antivírus")
            print("   - Assinar digitalmente o executável (requer certificado)")
            print("   - Contatar o suporte do antivírus para reportar o falso positivo")
        else:
            print_header("ERRO NA VERIFICAÇÃO")
            print("Ocorreu um erro ao verificar o executável.")
    else:
        print_header("ERRO NA COMPILAÇÃO")
        print("Ocorreu um erro durante a compilação do executável.")

    print("\nPressione Enter para sair...")
    input()

if __name__ == "__main__":
    main()
