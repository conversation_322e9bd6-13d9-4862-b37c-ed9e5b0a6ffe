#!/usr/bin/env python3
"""
Teste simples para verificar se conseguimos executar código Python
"""
print("=== TESTE SIMPLES ===")
print("Python está funcionando!")

try:
    import PyQt6
    print("PyQt6 importado com sucesso")
    try:
        print(f"PyQt6 versão: {PyQt6.QtCore.PYQT_VERSION_STR}")
    except:
        print("Versão do PyQt6 não disponível")
except ImportError as e:
    print(f"Erro ao importar PyQt6: {e}")

try:
    from PyQt6.QtWidgets import QApplication
    print("QApplication importada com sucesso")
except ImportError as e:
    print(f"Erro ao importar QApplication: {e}")

try:
    from resource_path import resource_path
    import os
    logo_path = resource_path('logo.png')
    print(f"Caminho da logo: {logo_path}")
    print(f"Logo existe: {os.path.exists(logo_path)}")
except Exception as e:
    print(f"Erro ao verificar logo: {e}")

print("=== FIM DO TESTE ===")
