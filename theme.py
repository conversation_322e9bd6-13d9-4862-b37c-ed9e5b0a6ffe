"""
Definições de tema e estilos para a aplicação PROSPECTO.
Sistema de captura de leads e automação de mensagens.
"""
from PyQt6.QtWidgets import (QWidget, QPushButton, QLineEdit, QLabel, QFrame,
                            QTabWidget, QGroupBox, QTextEdit, QComboBox)
from PyQt6.QtGui import QColor, QFont, QPalette, QIcon, QPixmap
from PyQt6.QtCore import Qt, QSize
import os

# Esquema de cores principal
# Cores principais
PRIMARY_COLOR = "#1E88E5"  # Azul principal
SECONDARY_COLOR = "#26A69A"  # Verde-azulado secundário
ACCENT_COLOR = "#FF5722"  # Laranja para destaque
BACKGROUND_COLOR = "#F5F7FA"  # Cinza claro para fundo
CARD_COLOR = "#FFFFFF"  # Branco para cards e containers

# Cores de texto
TEXT_PRIMARY = "#212121"  # Quase preto para texto principal
TEXT_SECONDARY = "#757575"  # Cinza para texto secundário
TEXT_HINT = "#9E9E9E"  # Cinza claro para dicas e placeholders

# Cores de estado
SUCCESS_COLOR = "#4CAF50"  # Verde para sucesso
ERROR_COLOR = "#F44336"  # Vermelho para erro
WARNING_COLOR = "#FFC107"  # Amarelo para avisos
INFO_COLOR = "#2196F3"  # Azul para informações

# Cores de botões
BUTTON_PRIMARY = PRIMARY_COLOR
BUTTON_SECONDARY = "#E0E0E0"  # Cinza claro para botões secundários
BUTTON_DISABLED = "#BDBDBD"  # Cinza para botões desabilitados

# Estilos de texto
FONT_FAMILY = "Roboto"
HEADING_SIZE = 24
SUBHEADING_SIZE = 18
BODY_SIZE = 14
CAPTION_SIZE = 12

# Espaçamento
PADDING_SMALL = 8
PADDING_MEDIUM = 16
PADDING_LARGE = 24
PADDING_XLARGE = 32

# Bordas
BORDER_RADIUS_SMALL = 4
BORDER_RADIUS_MEDIUM = 8
BORDER_RADIUS_LARGE = 12

def get_application_palette():
    """Retorna a paleta de cores para a aplicação."""
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(BACKGROUND_COLOR))
    palette.setColor(QPalette.ColorRole.WindowText, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.Base, QColor(CARD_COLOR))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(BACKGROUND_COLOR))
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(CARD_COLOR))
    palette.setColor(QPalette.ColorRole.ToolTipText, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.Text, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.Button, QColor(BUTTON_SECONDARY))
    palette.setColor(QPalette.ColorRole.ButtonText, QColor(TEXT_PRIMARY))
    palette.setColor(QPalette.ColorRole.BrightText, QColor(CARD_COLOR))
    palette.setColor(QPalette.ColorRole.Link, QColor(PRIMARY_COLOR))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(PRIMARY_COLOR))
    palette.setColor(QPalette.ColorRole.HighlightedText, QColor(CARD_COLOR))
    return palette

def get_application_stylesheet():
    """Retorna o stylesheet para a aplicação."""
    return f"""
        /* Estilo geral */
        QWidget {{
            font-family: {FONT_FAMILY}, Arial, sans-serif;
            font-size: {BODY_SIZE}px;
            color: {TEXT_PRIMARY};
        }}

        /* Botões */
        QPushButton {{
            background-color: {BUTTON_PRIMARY};
            color: white;
            border: none;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            font-weight: bold;
            min-height: 36px;
        }}

        QPushButton:hover {{
            background-color: #1976D2;  /* Versão mais escura do PRIMARY_COLOR */
        }}

        QPushButton:pressed {{
            background-color: #1565C0;  /* Versão ainda mais escura */
        }}

        QPushButton:disabled {{
            background-color: {BUTTON_DISABLED};
            color: #9E9E9E;
        }}

        /* Botão secundário */
        QPushButton[secondary="true"] {{
            background-color: {BUTTON_SECONDARY};
            color: {TEXT_PRIMARY};
        }}

        QPushButton[secondary="true"]:hover {{
            background-color: #D0D0D0;
        }}

        QPushButton[secondary="true"]:pressed {{
            background-color: #C0C0C0;
        }}

        /* Botão de acento */
        QPushButton[accent="true"] {{
            background-color: {ACCENT_COLOR};
        }}

        QPushButton[accent="true"]:hover {{
            background-color: #F4511E;
        }}

        QPushButton[accent="true"]:pressed {{
            background-color: #E64A19;
        }}

        /* Botão de sucesso */
        QPushButton[success="true"] {{
            background-color: {SUCCESS_COLOR};
        }}

        QPushButton[success="true"]:hover {{
            background-color: #43A047;
        }}

        QPushButton[success="true"]:pressed {{
            background-color: #388E3C;
        }}

        /* Botão de erro/perigo */
        QPushButton[danger="true"] {{
            background-color: {ERROR_COLOR};
        }}

        QPushButton[danger="true"]:hover {{
            background-color: #E53935;
        }}

        QPushButton[danger="true"]:pressed {{
            background-color: #D32F2F;
        }}

        /* Campos de texto */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px;
            background-color: {CARD_COLOR};
            selection-background-color: {PRIMARY_COLOR};
            selection-color: white;
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border: 2px solid {PRIMARY_COLOR};
        }}

        QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
            background-color: #F5F5F5;
            color: #9E9E9E;
        }}

        /* Abas */
        QTabWidget::pane {{
            border: 1px solid #BDBDBD;
            border-radius: {BORDER_RADIUS_SMALL}px;
            top: -1px;
        }}

        QTabBar::tab {{
            background-color: {BUTTON_SECONDARY};
            color: {TEXT_PRIMARY};
            border-top-left-radius: {BORDER_RADIUS_SMALL}px;
            border-top-right-radius: {BORDER_RADIUS_SMALL}px;
            padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
            margin-right: 2px;
        }}

        QTabBar::tab:selected {{
            background-color: {PRIMARY_COLOR};
            color: white;
        }}

        QTabBar::tab:hover:!selected {{
            background-color: #D0D0D0;
        }}

        /* Frames e Cards */
        QFrame[card="true"] {{
            background-color: {CARD_COLOR};
            border-radius: {BORDER_RADIUS_MEDIUM}px;
            border: 1px solid #E0E0E0;
        }}
    """

def style_heading_label(label):
    """Aplica estilo de título a um QLabel."""
    label.setStyleSheet(f"""
        font-size: {HEADING_SIZE}px;
        font-weight: bold;
        color: {TEXT_PRIMARY};
        margin-bottom: {PADDING_MEDIUM}px;
    """)

def style_subheading_label(label):
    """Aplica estilo de subtítulo a um QLabel."""
    label.setStyleSheet(f"""
        font-size: {SUBHEADING_SIZE}px;
        font-weight: bold;
        color: {TEXT_PRIMARY};
        margin-bottom: {PADDING_SMALL}px;
    """)

def style_body_label(label):
    """Aplica estilo de texto normal a um QLabel."""
    label.setStyleSheet(f"""
        font-size: {BODY_SIZE}px;
        color: {TEXT_SECONDARY};
    """)

def style_caption_label(label):
    """Aplica estilo de legenda a um QLabel."""
    label.setStyleSheet(f"""
        font-size: {CAPTION_SIZE}px;
        color: {TEXT_SECONDARY};
    """)

def create_card_frame():
    """Cria um QFrame estilizado como card."""
    frame = QFrame()
    frame.setProperty("card", "true")
    frame.setStyleSheet(f"""
        background-color: {CARD_COLOR};
        border-radius: {BORDER_RADIUS_MEDIUM}px;
        border: 1px solid #E0E0E0;
        padding: {PADDING_MEDIUM}px;
    """)
    return frame

def style_primary_button(button):
    """Aplica estilo de botão primário."""
    button.setStyleSheet(f"""
        background-color: {PRIMARY_COLOR};
        color: white;
        border: none;
        border-radius: {BORDER_RADIUS_SMALL}px;
        padding: {PADDING_SMALL}px {PADDING_MEDIUM}px;
        font-weight: bold;
        min-height: 36px;
    """)

def style_secondary_button(button):
    """Aplica estilo de botão secundário."""
    button.setProperty("secondary", "true")

def style_accent_button(button):
    """Aplica estilo de botão de destaque."""
    button.setProperty("accent", "true")

def get_logo_pixmap(size=QSize(80, 80)):
    """Retorna um QPixmap com o logo da aplicação."""
    pixmap = QPixmap("logo.png")
    return pixmap.scaled(size, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
